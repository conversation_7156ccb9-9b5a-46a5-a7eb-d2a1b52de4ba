import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import numpy as np
import joblib

# Process configuration: expected columns, model, scaler, and optimization type
PROCESS_CONFIG = {
    "Mining": {
        "features": [
            "Limestone_kg", "Clay_kg", "Additives_kg",
            "Rock_Hardness", "Excavation_Depth_m",
            "Ambient_Temperature_C", "Ambient_Humidity_pct"
        ],
        "target": "Mining_Energy_kWh",
        "optimize": "min",
        "model_file": "mining_model.pkl",
        "scaler_file": "mining_scaler.pkl"
    },
    "Burner": {
        "features": [
            "Kiln_Temperature_C", "Fuel_Liters", "Airflow_Cubic_m",
            "Burner_Pressure_bar", "Flame_Temperature_C",
            "Ambient_Temperature_C", "Ambient_Humidity_pct"
        ],
        "target": "Burner_Efficiency",
        "optimize": "max",
        "model_file": "burner_model.pkl",
        "scaler_file": "burner_scaler.pkl"
    },
    "Kiln": {
        "features": [
            "Clinker_Output_kg", "Kiln_Temperature_C", "Feed_Rate_kg_per_hr",
            "Rotation_Speed_rpm", "Oxygen_Level_pct",
            "Ambient_Temperature_C", "Ambient_Humidity_pct"
        ],
        "target": "Clinker_Quality",
        "optimize": "max",
        "model_file": "kiln_model.pkl",
        "scaler_file": "kiln_scaler.pkl"
    }
}

def process_file():
    process = process_var.get()
    if not process:
        messagebox.showerror("Error", "Please select a process.")
        return

    config = PROCESS_CONFIG[process]

    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        return

    try:
        data = pd.read_csv(file_path)
        for col in config["features"]:
            if col not in data.columns:
                raise ValueError(f"Missing required column: {col}")

        model = joblib.load(config["model_file"])
        scaler = joblib.load(config["scaler_file"])

        X = data[config["features"]]
        X_scaled = scaler.transform(X)
        y_pred = model.predict(X_scaled)

        if config["optimize"] == "min":
            best_idx = np.argmin(y_pred)
        else:
            best_idx = np.argmax(y_pred)

        best_input = X.iloc[best_idx]
        best_output = y_pred[best_idx]

        result_text = f"--- Optimal {process} Inputs ---\n"
        result_text += "\n".join([f"{k}: {v:.2f}" for k, v in best_input.items()])
        result_text += f"\n\nPredicted {config['target']}: {best_output:.2f}"

        result_label.config(text=result_text)
    except Exception as e:
        messagebox.showerror("Error", str(e))

# Setup GUI
root = tk.Tk()
root.title("Cement Plant Process Optimizer")

frame = tk.Frame(root, padx=20, pady=20)
frame.pack()

title = tk.Label(frame, text="Select Process and Upload CSV", font=("Helvetica", 14))
title.pack(pady=10)

process_var = tk.StringVar()
process_menu = tk.OptionMenu(frame, process_var, *PROCESS_CONFIG.keys())
process_menu.config(width=20)
process_menu.pack(pady=5)

upload_btn = tk.Button(frame, text="Upload CSV & Optimize", command=process_file, width=30)
upload_btn.pack(pady=10)

result_label = tk.Label(frame, text="", justify="left", font=("Courier", 10))
result_label.pack(pady=10)

root.mainloop()
