import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import joblib

# Load kiln dataset (replace this with your actual path if needed)
df = pd.read_csv("Kiln_Process_Dataset.csv")

# Define features and target
features = [
    "Clinker_Output_kg",
    "Kiln_Temperature_C",
    "Feed_Rate_kg_per_hr",
    "Rotation_Speed_rpm",
    "Oxygen_Level_pct",
    "Ambient_Temperature_C",
    "Ambient_Humidity_pct"
]
target = "Clinker_Quality"

X = df[features]
y = df[target]

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Split into train/test
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42
)

# Train the model
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# Evaluate
y_pred = model.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print("Kiln Model Performance:")
print(f"  Mean Squared Error: {mse:.2f}")
print(f"  R² Score: {r2:.4f}")

# Save model and scaler for UI use
joblib.dump(model, "kiln_model.pkl")
joblib.dump(scaler, "kiln_scaler.pkl")

print("Kiln model and scaler saved.")
