{"cells": [{"cell_type": "code", "execution_count": 4, "id": "075f55b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data preprocessing complete.\n", "X_train shape: (400, 12)\n", "Mean Squared Error: 110.89\n", "R^2 Score: -0.17\n", " Predicted optimal fuel (liters): 197.67\n"]}], "source": ["import pandas as pd\n", "from sklearn.preprocessing import LabelEncoder, StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "\n", "#  Read the data\n", "df = pd.read_csv(\"sample_cement_plant_data.csv\", parse_dates=[\"Timestamp\"])\n", "\n", "#  Drop timestamp\n", "df[\"Hour\"] = df[\"Timestamp\"].dt.hour\n", "df[\"DayOfWeek\"] = df[\"Timestamp\"].dt.dayofweek\n", "df = df.drop(columns=[\"Timestamp\"])\n", "\n", "#  Handle missing values\n", "df = df.fillna(method='ffill')  # forward fill\n", "\n", "#  Encode categorical columns\n", "categorical_cols = [\"Equipment_Status\", \"Product_Quality\"]\n", "label_encoders = {}\n", "for col in categorical_cols:\n", "    le = LabelEncoder()\n", "    df[col] = le.fit_transform(df[col])\n", "    label_encoders[col] = le\n", "\n", "#  Feature and target selection\n", "#  Predict Fuel_Liters as target\n", "X = df.drop(columns=[\"Fuel_Liters\"])\n", "y = df[\"Fuel_Liters\"]\n", "\n", "#  Feature scaling\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "#  Train-test split\n", "X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)\n", "\n", "print(\"Data preprocessing complete.\")\n", "print(f\"X_train shape: {X_train.shape}\")\n", "\n", "#FOR FUEL\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "\n", "#  Train a regression model\n", "model = RandomForestRegressor(n_estimators=100, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "#  Evaluate the model\n", "y_pred = model.predict(X_test)\n", "mse = mean_squared_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"Mean Squared Error: {mse:.2f}\")\n", "print(f\"R^2 Score: {r2:.2f}\")\n", "\n", "#   Predict optimal fuel for a new scenario\n", "# Example input (replace with actual values or integrate with real-time input later)\n", "import numpy as np\n", "\n", "sample_input = {\n", "    \"Limestone_kg\": 1180,\n", "    \"Clay_kg\": 295,\n", "    \"Additives_kg\": 48,\n", "    \"Kiln_Temperature_C\": 1445,\n", "    \"Electricity_kWh\": 240,\n", "    \"Clinker_Output_kg\": 970,\n", "    \"Ambient_Temperature_C\": 36,\n", "    \"Ambient_Humidity_pct\": 42,\n", "    \"Equipment_Status\": label_encoders[\"Equipment_Status\"].transform([\"Running\"])[0],\n", "    \"Product_Quality\": label_encoders[\"Product_Quality\"].transform([\"High\"])[0],\n", "    \"Hour\": 14,\n", "    \"DayOfWeek\": 3\n", "}\n", "\n", "sample_df = pd.DataFrame([sample_input])\n", "sample_scaled = scaler.transform(sample_df)\n", "predicted_fuel = model.predict(sample_scaled)[0]\n", "\n", "print(f\" Predicted optimal fuel (liters): {predicted_fuel:.2f}\")\n"]}, {"cell_type": "markdown", "id": "b350dcb8", "metadata": {}, "source": ["Optimization Goal:\n", "Minimize the total cost or resource usage-\n", "Formula is given in the image"]}, {"cell_type": "markdown", "id": "609ffc8d", "metadata": {}, "source": ["Optimal fuel:"]}, {"cell_type": "code", "execution_count": 6, "id": "cea2444f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " Optimized Input to Minimize Fuel Usage:\n", "Limestone_kg: 1261.32\n", "Clay_kg: 253.15\n", "Additives_kg: 43.61\n", "Kiln_Temperature_C: 1423.51\n", "Electricity_kWh: 276.69\n", "Clinker_Output_kg: 965.91\n", "Ambient_Temperature_C: 25.67\n", "Ambient_Humidity_pct: 22.29\n", "Equipment_Status: 2\n", "Product_Quality: 0\n", "Hour: 5.35\n", "DayOfWeek: 0.47\n", "\n", " Predicted Minimum Fuel Usage: 191.13 liters\n"]}], "source": ["import numpy as np\n", "\n", "# Generate synthetic test inputs (random search)\n", "n_trials = 1000\n", "best_input = None\n", "min_fuel = float('inf')\n", "\n", "\n", "raw_ranges = {\n", "    \"Limestone_kg\": (1100, 1300),\n", "    \"Clay_kg\": (250, 350),\n", "    \"Additives_kg\": (40, 60),\n", "    \"Kiln_Temperature_C\": (1420, 1480),\n", "    \"Electricity_kWh\": (200, 300),\n", "    \"Clinker_Output_kg\": (950, 1010),\n", "    \"Ambient_Temperature_C\": (25, 45),\n", "    \"Ambient_Humidity_pct\": (20, 60),\n", "    \"Equipment_Status\": [label_encoders[\"Equipment_Status\"].transform([\"Running\"])[0]],\n", "    \"Product_Quality\": [label_encoders[\"Product_Quality\"].transform([\"High\"])[0]],\n", "    \"Hour\": (0, 23),\n", "    \"DayOfWeek\": (0, 6),\n", "}\n", "\n", "for _ in range(n_trials):\n", "    sample = []\n", "    for key, val in raw_ranges.items():\n", "        if isinstance(val, tuple):\n", "            sample.append(np.random.uniform(*val))\n", "        else:\n", "            sample.append(np.random.choice(val))\n", "    \n", "    sample_scaled = scaler.transform([sample])\n", "    predicted_fuel = model.predict(sample_scaled)[0]\n", "    \n", "    if predicted_fuel < min_fuel:\n", "        min_fuel = predicted_fuel\n", "        best_input = sample\n", "\n", "# Output optimal input\n", "optimized_input = dict(zip(X.columns, best_input))\n", "print(\"\\n Optimized Input to Minimize Fuel Usage:\")\n", "for key, val in optimized_input.items():\n", "    print(f\"{key}: {val:.2f}\" if isinstance(val, float) else f\"{key}: {val}\")\n", "print(f\"\\n Predicted Minimum Fuel Usage: {min_fuel:.2f} liters\")\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "id": "8dca910b", "metadata": {}, "source": ["Optimal Electricity"]}, {"cell_type": "code", "execution_count": 11, "id": "39e34fd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔋 Optimal configuration to minimize electricity usage (while keeping clinker output > 960 kg):\n", "Limestone_kg                 1210.000\n", "Clay_kg                       310.000\n", "Additives_kg                   51.000\n", "Kiln_Temperature_C           1460.000\n", "Electricity_kWh                 0.000\n", "Clinker_Output_kg             970.000\n", "Ambient_Temperature_C          36.000\n", "Ambient_Humidity_pct           30.000\n", "Equipment_Status                2.000\n", "Product_Quality                 0.000\n", "Hour                           12.000\n", "DayOfWeek                       2.000\n", "Predicted_Electricity_kWh     179.483\n", "Name: 55552, dtype: float64\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from itertools import product\n", "\n", "# Step 1: Set up input ranges (for optimization)\n", "input_ranges = {\n", "    \"Limestone_kg\": np.arange(1150, 1250, 10),\n", "    \"Clay_kg\": np.arange(280, 320, 10),\n", "    \"Additives_kg\": np.arange(45, 55, 2),\n", "    \"Kiln_Temperature_C\": np.arange(1430, 1470, 10),\n", "    \"Clinker_Output_kg\": np.arange(950, 1000, 10),\n", "    \"Ambient_Temperature_C\": np.arange(30, 40, 2),\n", "    \"Ambient_Humidity_pct\": np.arange(30, 50, 5),\n", "    \"Equipment_Status\": label_encoders[\"Equipment_Status\"].transform([\"Running\"])[0:1],\n", "    \"Product_Quality\": label_encoders[\"Product_Quality\"].transform([\"High\"])[0:1],\n", "    \"Hour\": [12],\n", "    \"DayOfWeek\": [2]\n", "}\n", "\n", "# Step 2: Create a grid of all combinations\n", "keys, values = zip(*input_ranges.items())\n", "combinations = [dict(zip(keys, v)) for v in product(*values)]\n", "\n", "# Step 3: Convert to DataFrame and scale\n", "input_df = pd.DataFrame(combinations)\n", "input_df[\"Electricity_kWh\"] = 0\n", "X_columns = df.drop(columns=[\"Fuel_Liters\"]).columns  # same as X in earlier preprocessing\n", "input_df = input_df[X_columns]  # reorder columns\n", "\n", "# Step 4: Scale the inputs\n", "X_opt_scaled = scaler.transform(input_df)\n", "\n", "# Step 4: Train a model to predict electricity usage\n", "from sklearn.ensemble import RandomForestRegressor\n", "\n", "model_elec = RandomForestRegressor(n_estimators=100, random_state=42)\n", "model_elec.fit(X_train, df.loc[y_train.index, \"Electricity_kWh\"])  # Using original data\n", "\n", "# Step 5: Predict electricity usage on generated combinations\n", "electricity_predictions = model_elec.predict(X_opt_scaled)\n", "\n", "# Step 6: Combine predictions and filter by constraint (e.g., Clinker_Output > 960)\n", "input_df[\"Predicted_Electricity_kWh\"] = electricity_predictions\n", "feasible_results = input_df[input_df[\"Clinker_Output_kg\"] > 960]\n", "\n", "# Step 7: Find the optimal (minimum electricity usage)\n", "optimal_config = feasible_results.loc[feasible_results[\"Predicted_Electricity_kWh\"].idxmin()]\n", "\n", "print(\"🔋 Optimal configuration to minimize electricity usage (while keeping clinker output > 960 kg):\")\n", "print(optimal_config)\n"]}, {"cell_type": "markdown", "id": "21dc1c64", "metadata": {}, "source": ["Production scheduling:\n", "    Maximize production while:\n", "    Not exceeding raw material availability\n", "    Staying within machine time constraints\n", "    Meeting demand for different product types"]}, {"cell_type": "code", "execution_count": 12, "id": "4e117bc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛠️ Optimal Production Schedule\n", "Hour 1: Produ<PERSON> -> 0\n", "Hour 2: Pro<PERSON><PERSON> -> 0\n", "Hour 3: Pro<PERSON><PERSON> -> 0\n", "Hour 4: Produ<PERSON> -> 0\n", "Hour 5: Produ<PERSON> -> 0\n", "Hour 6: Pro<PERSON><PERSON> -> 0\n", "Hour 7: Pro<PERSON><PERSON> -> 0\n", "Hour 8: Produ<PERSON> -> 0\n", "Hour 9: Produ<PERSON> -> 0\n", "Hour 10: Produ<PERSON> -> 0\n", "\n", "✅ Total Batches Scheduled: 0\n", "💰 Estimated Total Profit: ₹-0.00\n"]}], "source": ["import pandas as pd\n", "from pulp import LpMaximize, LpProblem, LpVariable, lpSum\n", "\n", "# Example constants\n", "num_batches = 10\n", "batch_hours = 1  # hours per batch\n", "max_total_hours = 24  # shift duration\n", "\n", "# Constants (₹ per unit)\n", "fuel_cost_per_liter = 100      # ₹100 per liter\n", "electricity_cost_per_kwh = 8   # ₹8 per kWh\n", "limestone_cost = 1.5           # ₹1.5/kg\n", "clay_cost = 1.0                # ₹1/kg\n", "additive_cost = 5.0            # ₹5/kg\n", "revenue_per_ton = 8000         # ₹8000 per ton of clinker\n", "\n", "# Estimated usage per batch (avg values from dataset)\n", "fuel_usage_per_batch = 200    # liters\n", "electricity_usage_per_batch = 250  # kWh\n", "limestone_kg = 1200\n", "clay_kg = 300\n", "additives_kg = 50\n", "clinker_output_kg = 980\n", "\n", "# Decision variables: how many batches to run (0 to 1 for each hour slot)\n", "batches = [LpVariable(f\"batch_{i}\", lowBound=0, upBound=1, cat=\"Integer\") for i in range(num_batches)]\n", "\n", "# Define the LP problem\n", "model = LpProblem(\"Production_Scheduling\", LpMaximize)\n", "\n", "# Objective: Maximize total profit\n", "profit_per_batch = (\n", "    (clinker_output_kg / 1000) * revenue_per_ton -  # convert to tons\n", "    (fuel_usage_per_batch * fuel_cost_per_liter) -\n", "    (electricity_usage_per_batch * electricity_cost_per_kwh) -\n", "    (limestone_kg * limestone_cost) -\n", "    (clay_kg * clay_cost) -\n", "    (additives_kg * additive_cost)\n", ")\n", "\n", "model += lpSum([profit_per_batch * batch for batch in batches]), \"Total_Profit\"\n", "\n", "# Constraint: Maximum working hours\n", "model += lpSum([batch_hours * batch for batch in batches]) <= max_total_hours, \"Time_Constraint\"\n", "\n", "# Solve\n", "model.solve()\n", "\n", "# Results\n", "print(\"🛠️ Optimal Production Schedule\")\n", "total_batches = 0\n", "for i, var in enumerate(batches):\n", "    print(f\"Hour {i + 1}: Produce Batch -> {int(var.value())}\")\n", "    total_batches += int(var.value())\n", "\n", "total_profit = profit_per_batch * total_batches\n", "print(f\"\\n✅ Total Batches Scheduled: {total_batches}\")\n", "print(f\"💰 Estimated Total Profit: ₹{total_profit:.2f}\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "4634dcc7", "metadata": {}, "outputs": [], "source": ["fuel_cost_per_liter = 20       # ₹20 per liter (realistic bulk rate)\n", "electricity_cost_per_kwh = 6   # ₹6 per kWh (industrial rate)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "09ceb3ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estimated Profit per Batch: ₹480.00\n", "\n", "🛠️ Optimal Production Schedule\n", "Hour 1: <PERSON><PERSON><PERSON>\n", "Hour 2: <PERSON><PERSON><PERSON>\n", "Hour 3: <PERSON><PERSON><PERSON>\n", "Hour 4: <PERSON><PERSON><PERSON>\n", "Hour 5: <PERSON><PERSON><PERSON>\n", "Hour 6: <PERSON><PERSON><PERSON>\n", "Hour 7: <PERSON><PERSON><PERSON>\n", "Hour 8: <PERSON><PERSON><PERSON>\n", "\n", "✅ Total Batches Scheduled: 8\n", "💰 Estimated Total Profit: ₹3840.00\n"]}], "source": ["from pulp import LpMaximize, LpProblem, LpVariable, lpSum\n", "\n", "# Constants\n", "num_batches = 10\n", "batch_hours = 1\n", "max_total_hours = 8  # e.g., 8-hour shift\n", "\n", "# Revised cost values (₹)\n", "fuel_cost_per_liter = 20       # realistic industrial rate\n", "electricity_cost_per_kwh = 6\n", "limestone_cost = 1.5\n", "clay_cost = 1.0\n", "additive_cost = 5.0\n", "revenue_per_ton = 8500         # slightly increased revenue\n", "\n", "# Resource usage per batch\n", "fuel_usage_per_batch = 200     # liters\n", "electricity_usage_per_batch = 250  # kWh\n", "limestone_kg = 1200\n", "clay_kg = 300\n", "additives_kg = 50\n", "clinker_output_kg = 980\n", "\n", "# Calculate profit per batch\n", "profit_per_batch = (\n", "    (clinker_output_kg / 1000) * revenue_per_ton -\n", "    (fuel_usage_per_batch * fuel_cost_per_liter) -\n", "    (electricity_usage_per_batch * electricity_cost_per_kwh) -\n", "    (limestone_kg * limestone_cost) -\n", "    (clay_kg * clay_cost) -\n", "    (additives_kg * additive_cost)\n", ")\n", "\n", "print(f\"Estimated Profit per Batch: ₹{profit_per_batch:.2f}\")\n", "\n", "# Create model\n", "model = LpProblem(\"Cement_Production_Scheduling\", LpMaximize)\n", "\n", "# Decision variables: one for each hour slot\n", "batches = [LpVariable(f\"batch_{i}\", lowBound=0, upBound=1, cat=\"Integer\") for i in range(num_batches)]\n", "\n", "# Objective\n", "model += lpSum([profit_per_batch * batch for batch in batches]), \"Total_Profit\"\n", "\n", "# Time constraint\n", "model += lpSum([batch_hours * batch for batch in batches]) <= max_total_hours\n", "\n", "# Solve\n", "model.solve()\n", "\n", "# Output\n", "print(\"\\n🛠️ Optimal Production Schedule\")\n", "total_batches = 0\n", "for i, var in enumerate(batches):\n", "    if var.value() == 1:\n", "        print(f\"Hour {i + 1}: Produce Batch\")\n", "        total_batches += 1\n", "\n", "total_profit = profit_per_batch * total_batches\n", "print(f\"\\n✅ Total Batches Scheduled: {total_batches}\")\n", "print(f\"💰 Estimated Total Profit: ₹{total_profit:.2f}\")\n"]}, {"cell_type": "markdown", "id": "0b91294f", "metadata": {}, "source": ["Kiln Operation Optimization with Disturbances\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "e9e97af9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔥 Optimized Kiln Operation Schedule with Disturbances\n", "--------------------------------------------------------\n", "Hour  Setpoint (°C)   Disturbance (°C)    Actual Temp (°C)\n", "--------------------------------------------------------\n", "1     1445.0          5.0                 1450.0\n", "2     1451.4          -1.4                1450.0\n", "3     1443.5          6.5                 1450.0\n", "4     1436.1          15.2                1451.3\n", "5     1451.1          -2.3                1448.7\n", "6     1450.8          -2.3                1448.4\n", "7     1435.8          15.8                1451.6\n", "8     1442.3          7.7                 1450.0\n", "9     1454.7          -4.7                1450.0\n", "10    1444.6          5.4                 1450.0\n", "11    1454.6          -4.6                1450.0\n", "12    1454.7          -4.7                1450.0\n", "--------------------------------------------------------\n", "💰 Total Operational Cost: ₹9.86\n"]}], "source": ["import cvxpy as cp\n", "import numpy as np\n", "\n", "# Parameters\n", "num_hours = 12\n", "T_optimal = 1450\n", "T_min = 1350\n", "T_max = 1550\n", "downtime_bounds = (1400, 1500)\n", "\n", "# Cost weights\n", "alpha = 1.2   # energy inefficiency penalty\n", "beta = 2000   # downtime risk penalty\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)\n", "\n", "# Simulated temperature disturbances (e.g., sensor drift, fuel variability)\n", "# Represents external noise added to the actual kiln temperature\n", "disturbances = np.random.normal(loc=0, scale=10, size=num_hours)  # ±10°C\n", "\n", "# Decision variable: kiln setpoint temperature per hour\n", "T = cp.Variable(num_hours)\n", "\n", "# Actual temperature seen by the system (after disturbances)\n", "actual_temp = T + disturbances\n", "\n", "# Energy cost: penalize deviation from optimal\n", "energy_cost = cp.sum(cp.square(actual_temp - T_optimal) * alpha)\n", "\n", "# Downtime penalty: large penalty if outside [1400, 1500]\n", "downtime_penalty = cp.sum(\n", "    cp.multiply(beta, cp.maximum(0, 1400 - actual_temp) + cp.maximum(0, actual_temp - 1500))\n", ")\n", "\n", "# Total cost\n", "total_cost = energy_cost + downtime_penalty\n", "\n", "# Constraints\n", "constraints = [\n", "    T >= T_min,\n", "    T <= T_max\n", "]\n", "\n", "# Smooth temperature transitions (optional): prevent >15°C jumps between hours\n", "for i in range(1, num_hours):\n", "    constraints.append(cp.abs(T[i] - T[i-1]) <= 15)\n", "\n", "# Define and solve the optimization problem\n", "problem = cp.Problem(cp.Minimize(total_cost), constraints)\n", "problem.solve()\n", "\n", "# Display results\n", "print(\"🔥 Optimized Kiln Operation Schedule with Disturbances\")\n", "print(\"--------------------------------------------------------\")\n", "print(f\"{'Hour':<6}{'Setpoint (°C)':<16}{'Disturbance (°C)':<20}{'Actual Temp (°C)'}\")\n", "print(\"--------------------------------------------------------\")\n", "for i in range(num_hours):\n", "    actual = T.value[i] + disturbances[i]\n", "    print(f\"{i+1:<6}{T.value[i]:<16.1f}{disturbances[i]:<20.1f}{actual:.1f}\")\n", "print(\"--------------------------------------------------------\")\n", "print(f\"💰 Total Operational Cost: ₹{problem.value:.2f}\")\n"]}, {"cell_type": "markdown", "id": "88bb6d79", "metadata": {}, "source": ["| Parameter             | Description                                       |\n", "| --------------------- | ------------------------------------------------- |\n", "| `N`                   | Total number of time slots (e.g., days or shifts) |\n", "| `maintenance_cost[i]` | Cost to do maintenance in time slot `i`           |\n", "| `production_loss[i]`  | Cost of lost production if maintenance happens    |\n", "| `min_gap`             | Minimum required gap between maintenance events   |\n", "| `max_maintenances`    | Max number of maintenances allowed in horizon     |\n"]}, {"cell_type": "code", "execution_count": 16, "id": "d9fbd3b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🛠️  Maintenance Schedule (1 = scheduled):\n", "Kiln      : [0 0 1 0 0 0 0]\n", "Crusher   : [0 0 0 0 0 1 0]\n", "Mill      : [0 0 0 1 0 0 0]\n", "Conveyor  : [0 0 0 0 0 0 1]\n", "Cooler    : [0 0 0 0 1 0 0]\n", "\n", "💰 Total Maintenance Cost: ₹9300.00\n"]}], "source": ["import cvxpy as cp\n", "import numpy as np\n", "\n", "# Days and equipment\n", "days = 7\n", "equipments = ['Kiln', 'Crusher', '<PERSON>', 'Con<PERSON>or', 'Cooler']\n", "num_equip = len(equipments)\n", "\n", "# Decision variable: binary matrix (equip x days), 1 if maintenance scheduled\n", "M = cp.Variable((num_equip, days), boolean=True)\n", "\n", "# Maintenance costs per equipment (criticality weighting)\n", "maint_cost = np.array([3000, 1500, 2000, 1000, 1800])  # per maintenance\n", "penalty_cost = np.array([8000, 4000, 5000, 3000, 4500])  # penalty if not maintained\n", "\n", "# Objective: minimize total cost = scheduled cost + penalty for missed\n", "scheduled_cost = cp.sum(cp.multiply(M, maint_cost[:, None]))\n", "\n", "# Penalty if equipment not maintained at least once\n", "missed_maint = 1 - cp.sum(M, axis=1)\n", "penalty = cp.sum(cp.multiply(penalty_cost, missed_maint))\n", "\n", "# Total cost\n", "total_cost = scheduled_cost + penalty\n", "\n", "# Constraints\n", "constraints = []\n", "\n", "# 1. Each equipment must be maintained at most once\n", "constraints += [cp.sum(M[i, :]) <= 1 for i in range(num_equip)]\n", "\n", "# 2. No more than 2 maintenances per day\n", "constraints += [cp.sum(M[:, j]) <= 2 for j in range(days)]\n", "\n", "# Solve the problem\n", "problem = cp.Problem(cp.Minimize(total_cost), constraints)\n", "problem.solve(solver=cp.ECOS_BB)  # Use ECOS_BB for binary support\n", "\n", "# Output the schedule\n", "print(\"🛠️  Maintenance Schedule (1 = scheduled):\")\n", "schedule = np.round(M.value).astype(int)\n", "for i, equip in enumerate(equipments):\n", "    print(f\"{equip:<10}: {schedule[i]}\")\n", "\n", "print(f\"\\n💰 Total Maintenance Cost: ₹{problem.value:.2f}\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "1edb4771", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Sample data:\n", "   vibration  temperature   pressure          rpm  age_days  failure\n", "0   0.549671    76.996777  27.974465  1309.219244       151        1\n", "1   0.486174    74.623168  29.566444  1413.961499        50        0\n", "2   0.564769    70.298152  27.622740  1458.639447         9        0\n", "3   0.652303    66.765316  29.076115  1688.768766       119        0\n", "4   0.476585    73.491117  24.319156  1555.655312       148        0\n", "\n", "📊 Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.99      1.00      0.99       177\n", "           1       1.00      0.91      0.95        23\n", "\n", "    accuracy                           0.99       200\n", "   macro avg       0.99      0.96      0.97       200\n", "weighted avg       0.99      0.99      0.99       200\n", "\n", "\n", "📉 Confusion Matrix:\n", "[[177   0]\n", " [  2  21]]\n", "\n", "🧠 Predicted Failure Probabilities:\n", "Crusher   : 99.00% risk\n", "Kiln      : 0.00% risk\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "\n", "# Step 1: Generate Synthetic Sensor Data\n", "np.random.seed(42)\n", "n_samples = 1000\n", "\n", "data = pd.DataFrame({\n", "    'vibration': np.random.normal(0.5, 0.1, n_samples),\n", "    'temperature': np.random.normal(70, 5, n_samples),\n", "    'pressure': np.random.normal(30, 3, n_samples),\n", "    'rpm': np.random.normal(1500, 100, n_samples),\n", "    'age_days': np.random.randint(0, 180, n_samples),\n", "})\n", "\n", "# Generate failure label based on thresholds + noise\n", "# Simulates that failure is more likely with high vibration/temp/pressure and equipment age\n", "data['failure'] = (\n", "    (data['vibration'] > 0.6).astype(int) +\n", "    (data['temperature'] > 75).astype(int) +\n", "    (data['pressure'] > 33).astype(int) +\n", "    (data['age_days'] > 150).astype(int)\n", ")\n", "\n", "# Convert to binary: failure if 2+ risk conditions met\n", "data['failure'] = (data['failure'] >= 2).astype(int)\n", "\n", "print(\"🔍 Sample data:\")\n", "print(data.head())\n", "\n", "# Step 2: Train a Random Forest Classifier\n", "X = data[['vibration', 'temperature', 'pressure', 'rpm', 'age_days']]\n", "y = data['failure']\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "model = RandomForestClassifier(n_estimators=100, random_state=42)\n", "model.fit(X_train, y_train)\n", "\n", "# Step 3: Eva<PERSON>ate the Model\n", "y_pred = model.predict(X_test)\n", "print(\"\\n📊 Classification Report:\")\n", "print(classification_report(y_test, y_pred))\n", "\n", "print(\"\\n📉 Confusion Matrix:\")\n", "print(confusion_matrix(y_test, y_pred))\n", "\n", "# Step 4: Predict Failure Probability for Live Data\n", "# Example \"current equipment status\" from sensor feed\n", "live_data = pd.DataFrame({\n", "    'vibration': [0.68, 0.45],\n", "    'temperature': [78, 72],\n", "    'pressure': [35, 29],\n", "    'rpm': [1480, 1520],\n", "    'age_days': [170, 120]\n", "}, index=['Crusher', 'Kiln'])\n", "\n", "probs = model.predict_proba(live_data)[:, 1]  # Probability of failure\n", "print(\"\\n🧠 Predicted Failure Probabilities:\")\n", "for eq, prob in zip(live_data.index, probs):\n", "    print(f\"{eq:<10}: {prob:.2%} risk\")\n", "\n", "# Step 5: Visualization\n", "plt.figure(figsize=(6,4))\n", "plt.bar(live_data.index, probs, color=['red' if p > 0.5 else 'green' for p in probs])\n", "plt.title(\"⚠️ Predicted Failure Risk (Threshold: 50%)\")\n", "plt.ylabel(\"Failure Probability\")\n", "plt.ylim(0, 1)\n", "plt.axhline(0.5, color='gray', linestyle='--', label='Risk Threshold')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "f49369e4", "metadata": {}, "source": ["Gantt Chart + Calendar Integration for Maintenance Scheduling"]}, {"cell_type": "code", "execution_count": 18, "id": "37c1b18a", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "# Sample maintenance schedule (you can use output from your optimizer here)\n", "schedule_data = [\n", "    {'equipment': 'Kiln', 'start': '2025-05-24', 'duration_days': 2},\n", "    {'equipment': 'Crusher', 'start': '2025-05-25', 'duration_days': 1},\n", "    {'equipment': 'Mill', 'start': '2025-05-26', 'duration_days': 2},\n", "    {'equipment': 'Conveyor', 'start': '2025-05-27', 'duration_days': 1},\n", "    {'equipment': 'Cooler', 'start': '2025-05-28', 'duration_days': 2},\n", "]\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(schedule_data)\n", "df['start'] = pd.to_datetime(df['start'])\n", "df['end'] = df['start'] + pd.to_timedelta(df['duration_days'], unit='d')\n"]}, {"cell_type": "code", "execution_count": 19, "id": "9448b679", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.dates as mdates\n", "\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "\n", "# Assign each task to a y-value\n", "equipments = df['equipment'].unique()\n", "y_labels = list(equipments)\n", "y_pos = range(len(y_labels))\n", "\n", "# Plot each task as a horizontal bar\n", "for idx, row in df.iterrows():\n", "    ax.barh(\n", "        y=row['equipment'],\n", "        width=(row['end'] - row['start']).days,\n", "        left=row['start'],\n", "        height=0.5,\n", "        align='center',\n", "        color='skyblue',\n", "        edgecolor='black'\n", "    )\n", "\n", "# Format x-axis\n", "ax.xaxis_date()\n", "ax.xaxis.set_major_locator(mdates.DayLocator())\n", "ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))\n", "\n", "plt.xlabel(\"Date\")\n", "plt.ylabel(\"Equipment\")\n", "plt.title(\"🛠️ Maintenance Schedule Gantt Chart\")\n", "plt.grid(True, which='major', axis='x', linestyle='--')\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "96d6f84c", "metadata": {}, "source": ["Inventory Optimisation"]}, {"cell_type": "code", "execution_count": 20, "id": "984b8a76", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Optimal Production and Inventory Plan:\n", "\n", "📦 Cement_Type_1\n", "Day  Production  Inventory\n", "  1  0.00      120.00\n", "  2  0.00      60.00\n", "  3  40.00      0.00\n", "  4  90.00      0.00\n", "  5  70.00      0.00\n", "  6  60.00      0.00\n", "  7  110.00      0.00\n", "\n", "📦 Cement_Type_2\n", "Day  Production  Inventory\n", "  1  0.00      50.00\n", "  2  10.00      0.00\n", "  3  40.00      0.00\n", "  4  80.00      0.00\n", "  5  70.00      0.00\n", "  6  60.00      0.00\n", "  7  50.00      0.00\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datetime import datetime, timedelta\n", "\n", "# ----------------------\n", "# STEP 1: Data Definition\n", "# ----------------------\n", "# Days in planning horizon\n", "n_days = 7\n", "\n", "# Products and storage limits\n", "products = ['Cement_Type_1', 'Cement_Type_2']\n", "storage_capacity = {'Cement_Type_1': 500, 'Cement_Type_2': 400}  # max tons\n", "initial_inventory = {'Cement_Type_1': 200, 'Cement_Type_2': 100}  # tons on day 0\n", "\n", "# Daily demand forecast (in tons)\n", "demand_forecast = {\n", "    'Cement_Type_1': [80, 60, 100, 90, 70, 60, 110],\n", "    'Cement_Type_2': [50, 60, 40, 80, 70, 60, 50]\n", "}\n", "\n", "# Production cost per ton\n", "production_cost = {'Cement_Type_1': 300, 'Cement_Type_2': 320}  # in INR\n", "\n", "# Storage holding cost per ton per day\n", "holding_cost = {'Cement_Type_1': 5, 'Cement_Type_2': 6}\n", "\n", "# Max daily production capacity (tons per product)\n", "max_daily_production = {'Cement_Type_1': 150, 'Cement_Type_2': 100}\n", "\n", "# --------------------------\n", "# STEP 2: Optimization Model\n", "# --------------------------\n", "# Decision variables\n", "P = {p: cp.Variable(n_days, integer=False) for p in products}  # production\n", "i = {p: cp.Variable(n_days + 1, integer=False) for p in products}  # inventory\n", "\n", "# Objective: Minimize total cost (production + holding)\n", "objective = cp.Minimize(\n", "    sum([\n", "        cp.sum(P[p] * production_cost[p]) +\n", "        cp.sum(i[p][1:] * holding_cost[p])\n", "        for p in products\n", "    ])\n", ")\n", "\n", "# Constraints\n", "constraints = []\n", "\n", "for p in products:\n", "    # Initial inventory\n", "    constraints.append(i[p][0] == initial_inventory[p])\n", "\n", "    for t in range(n_days):\n", "        # Inventory balance\n", "        constraints.append(\n", "            i[p][t + 1] == i[p][t] + P[p][t] - demand_forecast[p][t]\n", "        )\n", "        # Non-negative inventory and production\n", "        constraints.append(P[p][t] >= 0)\n", "        constraints.append(i[p][t + 1] >= 0)\n", "        # Capacity constraint\n", "        constraints.append(P[p][t] <= max_daily_production[p])\n", "        constraints.append(i[p][t + 1] <= storage_capacity[p])\n", "\n", "# Solve problem\n", "problem = cp.Problem(objective, constraints)\n", "problem.solve(solver=cp.ECOS)  # or cp.SCS or cp.OSQP\n", "\n", "\n", "# ----------------------\n", "# STEP 3: Results Output\n", "# ----------------------\n", "print(\"\\nOptimal Production and Inventory Plan:\")\n", "for p in products:\n", "    print(f\"\\n📦 {p}\")\n", "    print(\"Day  Production  Inventory\")\n", "    for t in range(n_days):\n", "        print(f\"{t+1:>3}  {P[p][t].value:.2f}      {i[p][t+1].value:.2f}\")\n", "\n", "# ----------------------\n", "# STEP 4: Visualization\n", "# ----------------------\n", "fig, axs = plt.subplots(2, 1, figsize=(10, 6), sharex=True)\n", "days = np.arange(1, n_days + 1)\n", "\n", "for idx, p in enumerate(products):\n", "    axs[idx].plot(days, [P[p][t].value for t in range(n_days)], label='Production', marker='o')\n", "    axs[idx].plot(days, [i[p][t+1].value for t in range(n_days)], label='Inventory', marker='x')\n", "    axs[idx].set_title(f\"{p} - Production & Inventory\")\n", "    axs[idx].set_ylabel(\"Tons\")\n", "    axs[idx].legend()\n", "    axs[idx].grid(True)\n", "\n", "plt.xlabel(\"Day\")\n", "plt.tight_layout()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}