import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import joblib

# Load your burner dataset (replace with your actual path)
df = pd.read_csv("Burner_Process_Dataset.csv")  # Ensure this matches the generated dataset

# Define features and target
features = [
    "Kiln_Temperature_C",
    "Fuel_Liters",
    "Airflow_Cubic_m",
    "Burner_Pressure_bar",
    "Flame_Temperature_C",
    "Ambient_Temperature_C",
    "Ambient_Humidity_pct"
]
target = "Burner_Efficiency"

X = df[features]
y = df[target]

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Split into train/test
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42
)

# Train model
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# Evaluate
y_pred = model.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print("Burner Model Performance:")
print(f"  Mean Squared Error: {mse:.4f}")
print(f"  R² Score: {r2:.4f}")

# Save model and scaler
joblib.dump(model, "burner_model.pkl")
joblib.dump(scaler, "burner_scaler.pkl")

print("Burner model and scaler saved.")
