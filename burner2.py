import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import numpy as np
import joblib

# Load trained model and scaler
model = joblib.load("burner_model.pkl")
scaler = joblib.load("burner_scaler.pkl")

# Define expected input features
FEATURE_COLUMNS = [
    "Kiln_Temperature_C",
    "Fuel_Liters",
    "Airflow_Cubic_m",
    "Burner_Pressure_bar",
    "Flame_Temperature_C",
    "Ambient_Temperature_C",
    "Ambient_Humidity_pct"
]

# Optimization logic: find row with highest predicted efficiency
def find_best_efficiency_input(data):
    if not all(col in data.columns for col in FEATURE_COLUMNS):
        raise ValueError("Input CSV must contain all required columns.")

    X = data[FEATURE_COLUMNS]
    X_scaled = scaler.transform(X)
    y_pred = model.predict(X_scaled)

    best_idx = np.argmax(y_pred)
    best_input = X.iloc[best_idx]
    best_output = y_pred[best_idx]

    return best_input, best_output

# UI handler for file upload
def upload_file():
    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        return

    try:
        data = pd.read_csv(file_path)
        best_input, best_output = find_best_efficiency_input(data)

        result_text = "\n".join(
            [f"{key}: {value:.2f}" for key, value in best_input.items()]
        )
        result_text += f"\n\nPredicted Burner Efficiency: {best_output:.4f}"

        result_label.config(text=result_text)
    except Exception as e:
        messagebox.showerror("Error", str(e))

# Tkinter UI setup
root = tk.Tk()
root.title("Burner Efficiency Optimizer")

frame = tk.Frame(root, padx=20, pady=20)
frame.pack()

title = tk.Label(frame, text="Upload CSV to Find Optimal Burner Parameters", font=("Helvetica", 14))
title.pack(pady=10)

upload_button = tk.Button(frame, text="Upload Burner CSV", command=upload_file, width=25)
upload_button.pack(pady=10)

result_label = tk.Label(frame, text="", justify="left", font=("Courier", 10))
result_label.pack(pady=10)

root.mainloop()
