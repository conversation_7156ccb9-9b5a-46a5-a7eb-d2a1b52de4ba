import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import joblib

# Load your mining dataset (replace this with your actual path if needed)
df = pd.read_csv("Mining_Process_Dataset.csv")  # Ensure this matches the dataset structure

# Define features and target
features = [
    "Limestone_kg",
    "Clay_kg",
    "Additives_kg",
    "Rock_Hardness",
    "Excavation_Depth_m",
    "Ambient_Temperature_C",
    "Ambient_Humidity_pct"
]
target = "Mining_Energy_kWh"

X = df[features]
y = df[target]

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y, test_size=0.2, random_state=42
)

# Train the model
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# Evaluate the model
y_pred = model.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print(f"Model performance on test set:")
print(f"  Mean Squared Error: {mse:.2f}")
print(f"  R² Score: {r2:.2f}")

# Save the model and scaler for UI use
joblib.dump(model, "mining_model.pkl")
joblib.dump(scaler, "mining_scaler.pkl")

print("Model and scaler saved to disk.")
