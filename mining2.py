import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import numpy as np
import joblib
from sklearn.ensemble import RandomForestRegressor

# Load model and scaler
model = joblib.load("mining_model.pkl")
scaler = joblib.load("mining_scaler.pkl")

# Define feature columns expected by the model
FEATURE_COLUMNS = [
    "Limestone_kg",
    "Clay_kg",
    "Additives_kg",
    "Rock_Hardness",
    "Excavation_Depth_m",
    "Ambient_Temperature_C",
    "Ambient_Humidity_pct"
]

# Optimization function
def find_optimal_input(data):
    if not all(col in data.columns for col in FEATURE_COLUMNS):
        raise ValueError("Missing required columns in input CSV")

    X = data[FEATURE_COLUMNS]
    X_scaled = scaler.transform(X)
    y_pred = model.predict(X_scaled)

    # Find the index of the row with the lowest predicted energy usage
    best_idx = np.argmin(y_pred)
    optimal_input = X.iloc[best_idx]
    optimal_output = y_pred[best_idx]
    return optimal_input, optimal_output

# Tkinter GUI
def upload_file():
    file_path = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
    if not file_path:
        return

    try:
        data = pd.read_csv(file_path)
        optimal_input, optimal_output = find_optimal_input(data)

        result_text = "\n".join(
            [f"{key}: {value:.2f}" for key, value in optimal_input.items()]
        )
        result_text += f"\n\nPredicted Mining_Energy_kWh: {optimal_output:.2f}"

        result_label.config(text=result_text)
    except Exception as e:
        messagebox.showerror("Error", str(e))

# GUI setup
root = tk.Tk()
root.title("Mining Energy Optimizer")

frame = tk.Frame(root, padx=20, pady=20)
frame.pack()

title = tk.Label(frame, text="Upload CSV to Find Optimal Mining Parameters", font=("Helvetica", 14))
title.pack(pady=10)

upload_button = tk.Button(frame, text="Upload CSV", command=upload_file, width=20)
upload_button.pack(pady=10)

result_label = tk.Label(frame, text="", justify="left", font=("Courier", 10))
result_label.pack(pady=10)

root.mainloop()
